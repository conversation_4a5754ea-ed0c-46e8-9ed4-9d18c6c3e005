<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Interactive Stencil Buffer</title>
        <link href="assets/main.css" rel="stylesheet" />
        <style>
            .controls {
                position: absolute;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                font-family: monospace;
                font-size: 12px;
                min-width: 250px;
                z-index: 1000;
            }
            .control-group {
                margin-bottom: 15px;
            }
            .control-group label {
                display: block;
                margin-bottom: 5px;
                color: #ccc;
            }
            .control-group input,
            .control-group select {
                width: 100%;
                padding: 5px;
                margin-bottom: 5px;
                background: #333;
                color: white;
                border: 1px solid #555;
                border-radius: 3px;
            }
            .control-group button {
                width: 100%;
                padding: 8px;
                background: #4caf50;
                color: white;
                border: none;
                border-radius: 3px;
                cursor: pointer;
                margin-bottom: 5px;
            }
            .control-group button:hover {
                background: #45a049;
            }
            .control-group button.active {
                background: #2196f3;
            }
            .value-display {
                color: #4caf50;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="Info">
            Interactive Stencil Buffer - 交互式模版缓冲区<br />
            <small>
                学习模版缓冲区的各种功能和参数<br />
                使用右侧控制面板调整遮罩设置
            </small>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>遮罩形状 (Mask Shape)</label>
                <select id="maskShape">
                    <option value="box">立方体 (Box)</option>
                    <option value="sphere">球体 (Sphere)</option>
                    <option value="plane">平面 (Plane)</option>
                    <option value="cylinder">圆柱体 (Cylinder)</option>
                </select>
            </div>

            <div class="control-group">
                <label>遮罩模式 (Mask Mode)</label>
                <button id="maskMode" class="active">内部遮罩 (Inside)</button>
            </div>

            <div class="control-group">
                <label>遮罩大小 (Scale): <span id="scaleValue" class="value-display">1.0</span></label>
                <input type="range" id="maskScale" min="0.5" max="3.0" step="0.1" value="1.0" />
            </div>

            <div class="control-group">
                <label>遮罩位置 X: <span id="posXValue" class="value-display">0.0</span></label>
                <input type="range" id="maskPosX" min="-2.0" max="2.0" step="0.1" value="0.0" />
            </div>

            <div class="control-group">
                <label>遮罩位置 Y: <span id="posYValue" class="value-display">0.0</span></label>
                <input type="range" id="maskPosY" min="-2.0" max="2.0" step="0.1" value="0.0" />
            </div>

            <div class="control-group">
                <label>旋转速度: <span id="rotSpeedValue" class="value-display">1.0</span></label>
                <input type="range" id="rotationSpeed" min="0.0" max="3.0" step="0.1" value="1.0" />
            </div>

            <div class="control-group">
                <label>显示选项 (Display Options)</label>
                <button id="showMask">显示遮罩轮廓</button>
                <button id="showStencil">显示模版缓冲区</button>
                <button id="animateColors">动画颜色</button>
            </div>

            <div class="control-group">
                <label>预设效果 (Presets)</label>
                <button id="preset1">镜面效果</button>
                <button id="preset2">窗口效果</button>
                <button id="preset3">多重遮罩</button>
            </div>
        </div>

        <script type="module">
            import { Renderer, Program, Color, Mesh, Plane, Box, Sphere, Cylinder, Camera, Transform, Vec3, Orbit } from '../src/index.js';

            // 基础顶点着色器
            const vertex = /* glsl */ `
                attribute vec3 position;
                attribute vec2 uv;
                attribute vec3 normal;

                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;
                uniform mat3 normalMatrix;

                varying vec2 vUv;
                varying vec3 vNormal;

                void main() {
                    vUv = uv;
                    vNormal = normalize(normalMatrix * normal);
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // 遮罩着色器（用于写入模版缓冲区）
            const maskFragment = /* glsl */ `
                precision highp float;
                
                uniform vec3 uMaskColor;
                uniform bool uShowMask;
                
                varying vec3 vNormal;
                
                void main() {
                    if (uShowMask) {
                        // 显示遮罩轮廓时使用简单光照
                        vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
                        float diff = max(dot(vNormal, lightDir), 0.2);
                        gl_FragColor = vec4(uMaskColor * diff, 0.5);
                    } else {
                        // 正常遮罩模式，不显示颜色
                        gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);
                    }
                }
            `;

            // 彩色着色器（用于渲染彩色内容）
            const colorFragment = /* glsl */ `
                precision highp float;

                uniform float uTime;
                uniform vec3 uColor;
                uniform bool uAnimateColors;
                
                varying vec2 vUv;

                void main() {
                    vec3 color = uColor;
                    
                    if (uAnimateColors) {
                        // 创建动态彩色效果
                        color += 0.3 * cos(vUv.xyx * 10.0 + uTime);
                        color = mix(color, vec3(sin(uTime), cos(uTime * 1.3), sin(uTime * 0.7)) * 0.5 + 0.5, 0.3);
                    }
                    
                    gl_FragColor = vec4(color, 1.0);
                }
            `;

            // 背景着色器
            const backgroundFragment = /* glsl */ `
                precision highp float;

                uniform float uTime;
                uniform bool uAnimateColors;
                
                varying vec2 vUv;

                void main() {
                    vec2 uv = vUv * 2.0 - 1.0;
                    float d = length(uv);
                    
                    vec3 color = vec3(0.1, 0.2, 0.4);
                    
                    if (uAnimateColors) {
                        color += 0.1 * sin(d * 10.0 - uTime * 2.0);
                        color = mix(color, vec3(0.2, 0.1, 0.3), sin(uTime * 0.5) * 0.5 + 0.5);
                    }
                    
                    gl_FragColor = vec4(color, 1.0);
                }
            `;

            // 模版缓冲区可视化着色器
            const stencilVisFragment = /* glsl */ `
                precision highp float;
                
                void main() {
                    // 显示模版缓冲区为红色
                    gl_FragColor = vec4(1.0, 0.0, 0.0, 0.3);
                }
            `;

            // 初始化渲染器
            const renderer = new Renderer({
                stencil: true, // 启用模版缓冲区
                alpha: true, // 启用透明度
            });
            const gl = renderer.gl;
            document.body.appendChild(gl.canvas);
            gl.clearColor(0, 0, 0, 1);

            // 创建相机
            const camera = new Camera(gl, { fov: 45 });
            camera.position.set(0, 0, 5);

            // 创建轨道控制器
            const controls = new Orbit(camera, {
                target: new Vec3(0, 0, 0),
            });

            function resize() {
                renderer.setSize(window.innerWidth, window.innerHeight);
                camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
            }
            window.addEventListener('resize', resize, false);
            resize();

            // 创建几何体
            const geometries = {
                plane: new Plane(gl),
                box: new Box(gl),
                sphere: new Sphere(gl),
                cylinder: new Cylinder(gl),
            };

            // 应用状态
            const appState = {
                maskShape: 'box',
                maskMode: 'inside', // 'inside' or 'outside'
                maskScale: 1.0,
                maskPosX: 0.0,
                maskPosY: 0.0,
                rotationSpeed: 1.0,
                showMask: false,
                showStencil: false,
                animateColors: true,
            };

            // 创建程序
            function createPrograms() {
                // 遮罩程序
                const maskProgram = new Program(gl, {
                    vertex,
                    fragment: maskFragment,
                    uniforms: {
                        uMaskColor: { value: new Color(1.0, 1.0, 0.0) },
                        uShowMask: { value: false },
                    },
                    depthWrite: false,
                    transparent: true,
                    depthTest: false,
                });

                // 彩色程序
                const colorProgram = new Program(gl, {
                    vertex,
                    fragment: colorFragment,
                    uniforms: {
                        uTime: { value: 0 },
                        uColor: { value: new Color(0.8, 0.3, 0.5) },
                        uAnimateColors: { value: true },
                    },
                });

                // 背景程序
                const backgroundProgram = new Program(gl, {
                    vertex,
                    fragment: backgroundFragment,
                    uniforms: {
                        uTime: { value: 0 },
                        uAnimateColors: { value: true },
                    },
                });

                // 模版可视化程序
                const stencilVisProgram = new Program(gl, {
                    vertex,
                    fragment: stencilVisFragment,
                    transparent: true,
                    depthTest: false,
                });

                return { maskProgram, colorProgram, backgroundProgram, stencilVisProgram };
            }

            const programs = createPrograms();

            // 更新模版测试设置
            function updateStencilSettings() {
                const { maskProgram, colorProgram, backgroundProgram, stencilVisProgram } = programs;

                if (appState.maskMode === 'inside') {
                    // 内部遮罩模式
                    maskProgram.setStencilFunc(gl.ALWAYS, 1, 0xff);
                    maskProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);

                    colorProgram.setStencilFunc(gl.EQUAL, 1, 0xff);
                    colorProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);

                    backgroundProgram.setStencilFunc(gl.NOTEQUAL, 1, 0xff);
                    backgroundProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);
                } else {
                    // 外部遮罩模式
                    maskProgram.setStencilFunc(gl.ALWAYS, 1, 0xff);
                    maskProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);

                    colorProgram.setStencilFunc(gl.NOTEQUAL, 1, 0xff);
                    colorProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);

                    backgroundProgram.setStencilFunc(gl.EQUAL, 1, 0xff);
                    backgroundProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);
                }

                // 模版可视化程序
                stencilVisProgram.setStencilFunc(gl.EQUAL, 1, 0xff);
                stencilVisProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);
            }

            // 创建场景对象
            const scene = new Transform();
            let currentMaskMesh;

            // 创建遮罩对象
            function createMaskMesh() {
                if (currentMaskMesh) {
                    scene.removeChild(currentMaskMesh);
                }

                const geometry = geometries[appState.maskShape];
                currentMaskMesh = new Mesh(gl, {
                    geometry,
                    program: programs.maskProgram,
                });
                currentMaskMesh.setParent(scene);

                return currentMaskMesh;
            }

            // 创建其他对象
            const colorPlane = new Mesh(gl, {
                geometry: geometries.plane,
                program: programs.colorProgram,
            });
            colorPlane.position.z = -1;
            colorPlane.scale.set(3);
            colorPlane.setParent(scene);

            const backgroundPlane = new Mesh(gl, {
                geometry: geometries.plane,
                program: programs.backgroundProgram,
            });
            backgroundPlane.position.z = -2;
            backgroundPlane.scale.set(4);
            backgroundPlane.setParent(scene);

            // 模版可视化平面
            const stencilVisPlane = new Mesh(gl, {
                geometry: geometries.plane,
                program: programs.stencilVisProgram,
            });
            stencilVisPlane.position.z = -0.5;
            stencilVisPlane.scale.set(3);
            stencilVisPlane.setParent(scene);

            // 初始化
            createMaskMesh();
            updateStencilSettings();

            // 控制面板事件处理
            function setupControls() {
                // 遮罩形状选择
                document.getElementById('maskShape').addEventListener('change', (e) => {
                    appState.maskShape = e.target.value;
                    createMaskMesh();
                });

                // 遮罩模式切换
                document.getElementById('maskMode').addEventListener('click', (e) => {
                    appState.maskMode = appState.maskMode === 'inside' ? 'outside' : 'inside';
                    e.target.textContent = appState.maskMode === 'inside' ? '内部遮罩 (Inside)' : '外部遮罩 (Outside)';
                    e.target.className = appState.maskMode === 'inside' ? 'active' : '';
                    updateStencilSettings();
                });

                // 遮罩大小
                document.getElementById('maskScale').addEventListener('input', (e) => {
                    appState.maskScale = parseFloat(e.target.value);
                    document.getElementById('scaleValue').textContent = appState.maskScale.toFixed(1);
                });

                // 遮罩位置 X
                document.getElementById('maskPosX').addEventListener('input', (e) => {
                    appState.maskPosX = parseFloat(e.target.value);
                    document.getElementById('posXValue').textContent = appState.maskPosX.toFixed(1);
                });

                // 遮罩位置 Y
                document.getElementById('maskPosY').addEventListener('input', (e) => {
                    appState.maskPosY = parseFloat(e.target.value);
                    document.getElementById('posYValue').textContent = appState.maskPosY.toFixed(1);
                });

                // 旋转速度
                document.getElementById('rotationSpeed').addEventListener('input', (e) => {
                    appState.rotationSpeed = parseFloat(e.target.value);
                    document.getElementById('rotSpeedValue').textContent = appState.rotationSpeed.toFixed(1);
                });

                // 显示遮罩轮廓
                document.getElementById('showMask').addEventListener('click', (e) => {
                    appState.showMask = !appState.showMask;
                    e.target.className = appState.showMask ? 'active' : '';
                    programs.maskProgram.uniforms.uShowMask.value = appState.showMask;
                });

                // 显示模版缓冲区
                document.getElementById('showStencil').addEventListener('click', (e) => {
                    appState.showStencil = !appState.showStencil;
                    e.target.className = appState.showStencil ? 'active' : '';
                });

                // 动画颜色
                document.getElementById('animateColors').addEventListener('click', (e) => {
                    appState.animateColors = !appState.animateColors;
                    e.target.className = appState.animateColors ? 'active' : '';
                    programs.colorProgram.uniforms.uAnimateColors.value = appState.animateColors;
                    programs.backgroundProgram.uniforms.uAnimateColors.value = appState.animateColors;
                });

                // 预设效果
                document.getElementById('preset1').addEventListener('click', () => {
                    // 镜面效果
                    appState.maskShape = 'plane';
                    appState.maskMode = 'inside';
                    appState.maskScale = 2.0;
                    appState.maskPosY = -1.0;
                    applyPreset();
                });

                document.getElementById('preset2').addEventListener('click', () => {
                    // 窗口效果
                    appState.maskShape = 'box';
                    appState.maskMode = 'outside';
                    appState.maskScale = 1.5;
                    appState.maskPosX = 0.0;
                    appState.maskPosY = 0.0;
                    applyPreset();
                });

                document.getElementById('preset3').addEventListener('click', () => {
                    // 多重遮罩效果
                    appState.maskShape = 'sphere';
                    appState.maskMode = 'inside';
                    appState.maskScale = 1.2;
                    appState.rotationSpeed = 2.0;
                    applyPreset();
                });
            }

            function applyPreset() {
                // 更新UI控件
                document.getElementById('maskShape').value = appState.maskShape;
                document.getElementById('maskScale').value = appState.maskScale;
                document.getElementById('maskPosX').value = appState.maskPosX;
                document.getElementById('maskPosY').value = appState.maskPosY;
                document.getElementById('rotationSpeed').value = appState.rotationSpeed;

                // 更新显示值
                document.getElementById('scaleValue').textContent = appState.maskScale.toFixed(1);
                document.getElementById('posXValue').textContent = appState.maskPosX.toFixed(1);
                document.getElementById('posYValue').textContent = appState.maskPosY.toFixed(1);
                document.getElementById('rotSpeedValue').textContent = appState.rotationSpeed.toFixed(1);

                // 更新按钮状态
                const modeBtn = document.getElementById('maskMode');
                modeBtn.textContent = appState.maskMode === 'inside' ? '内部遮罩 (Inside)' : '外部遮罩 (Outside)';
                modeBtn.className = appState.maskMode === 'inside' ? 'active' : '';

                // 重新创建遮罩和更新设置
                createMaskMesh();
                updateStencilSettings();
            }

            setupControls();

            // 渲染循环
            requestAnimationFrame(update);
            function update(t) {
                requestAnimationFrame(update);

                const time = t * 0.001;

                // 更新轨道控制器
                controls.update();

                // 更新uniform
                programs.colorProgram.uniforms.uTime.value = time;
                programs.backgroundProgram.uniforms.uTime.value = time;

                // 更新遮罩对象
                if (currentMaskMesh) {
                    // 应用用户设置
                    currentMaskMesh.scale.setScalar(appState.maskScale);
                    currentMaskMesh.position.x = appState.maskPosX;
                    currentMaskMesh.position.y = appState.maskPosY;

                    // 旋转动画
                    currentMaskMesh.rotation.x = time * 0.5 * appState.rotationSpeed;
                    currentMaskMesh.rotation.y = time * 0.7 * appState.rotationSpeed;
                }

                // 清除缓冲区（包括模版缓冲区）
                gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT | gl.STENCIL_BUFFER_BIT);

                // 第一步：渲染遮罩到模版缓冲区
                if (appState.showMask) {
                    // 显示遮罩轮廓
                    renderer.render({ scene: currentMaskMesh, camera });
                } else {
                    // 只写入模版缓冲区，不显示颜色
                    gl.colorMask(false, false, false, false);
                    renderer.render({ scene: currentMaskMesh, camera });
                    gl.colorMask(true, true, true, true);
                }

                // 第二步：根据模版缓冲区渲染内容
                if (appState.maskMode === 'inside') {
                    // 内部遮罩：在遮罩区域显示彩色，外部显示背景
                    renderer.render({ scene: colorPlane, camera, clear: false });
                    renderer.render({ scene: backgroundPlane, camera, clear: false });
                } else {
                    // 外部遮罩：在遮罩区域显示背景，外部显示彩色
                    renderer.render({ scene: backgroundPlane, camera, clear: false });
                    renderer.render({ scene: colorPlane, camera, clear: false });
                }

                // 第三步：可选显示模版缓冲区可视化
                if (appState.showStencil) {
                    renderer.render({ scene: stencilVisPlane, camera, clear: false });
                }
            }
        </script>
    </body>
</html>
